import { HTMLProps, ReactNode } from "react";
import styles from "./{{kebabCase componentName}}.module.scss";

export interface {{capitalize componentName}}Props extends HTMLProps<HTMLDivElement> {
	children?: ReactNode;
}

/**
 * {{ description }}
 *
 * @example
 * ```tsx
 * <{{capitalize componentName}} />
 * ```
 * 
 * @source - Source code
 */
export const {{capitalize componentName}} = ({ children, ...props }: {{capitalize componentName}}Props) => {
  const className = [props.className, styles["{{kebabCase componentName}}"]].filter(Boolean).join(" ");
	return (
		<div {...props} className={className} data-testid="{{kebabCase componentName}}">
			{children}
		</div>
	);
}
