import { useState } from "react";

export interface {{capitalize componentName}}Options {
  /** this is a dummy option */
  dummy?: string;
}

/**
 * {{ description }}
 *
 * @example
 * ```tsx
 * const [] = {{ camelCase name }}(options);
 * ```
 * 
 * @source - Source code
 */

export const {{ camelCase name }} = (options?: {{capitalize componentName}}Options) => {
  const [value, setValue] = useState(0);
  return {
    value, setValue
  }
}