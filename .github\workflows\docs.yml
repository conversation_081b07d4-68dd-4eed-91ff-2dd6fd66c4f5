name: Update dependencies and Docs

on:
  push:
    branches: [main]
  schedule:
    - cron: "0 */8 * * *"
jobs:
  generate-docs:
    if: github.event.repository.owner.login == 'webriq:webriq'
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/setup-node@v4
        with:
          registry-url: https://registry.npmjs.org
          node-version: 20
      - name: Setup Git
        run: |
          git config --global user.name webriq
          git config --global user.email "<EMAIL>"
          git fetch
          git checkout main
          git pull
      - run: npm i -g pnpm && pnpm i --no-frozen-lockfile
        name: Install dependencies
      - name: Test
        run: npm test
      - run: git stash --include-untracked
        name: clean up working directory
      - run: npx @turbo/codemod update . && pnpm update --latest -r
        name: Update dependencies
      - run: pnpm build
        name: Build all apps to make sure it is not broken due to dependency upgrades
      - name: Run unit tests
        run: pnpm test
      - name: Generate/update docs
        run: pnpm doc
      - name: Save upgraded packages back to repo
        run: git add . && git commit -m "upgrade deps && docs" && git push origin main
