# Featured packages built with this template.

> This file is automatically generated. Please refrain from editing it directly. To add your package, update `scripts/featured.json` in alphabetical order.

- [esbuild-plugin-react18](https://github.com/react18-tools/esbuild-plugin-react18) - An esbuild plugin for compiling libraries compatible with React 18 server and client component, Nextjs13, and Nextjs14
- [esbuild-plugin-react18-css](https://github.com/react18-tools/esbuild-plugin-react18-css) - ESBuild plugin to handle CSS/SCSS modules, autoprefixer, etc.
- [Nextjs-Themes](https://github.com/react18-tools/nextjs-themes) - 🤟 👉 Theme with confidence and Unleash the Power of React Server Components
- [Persist-And-Sync](https://github.com/react18-tools/persist-and-sync) - Zustand middleware to easily persist and sync Zustand state between tabs / windows / iframes (Same Origin)
- [React 18 Themes](https://github.com/react18-tools/react18-themes) - 🤟 👉 Unleash the Power of React Server Components
- [React18 Global Store](https://github.com/react18-tools/react18-global-store) - A simple yet elegant, light weight, react18 global store to replace Zustand for better tree shaking.
- [Zustand Sync Tabs](https://github.com/react18-tools/zustand-sync-tabs) - Zustand middleware to easily sync Zustand state between tabs / windows / iframes (Same Origin)
