{"name": "@apps/storybook", "version": "0.0.1", "private": true, "scripts": {"dev": "pnpm run storybook", "storybook": "storybook dev -p 6006", "build": "storybook build --docs", "build-storybook": "storybook build --docs", "preview-storybook": "serve storybook-static", "clean": "rm -rf .turbo && rm -rf node_modules storybook-static .turbo", "lint": "eslint ../**/*.stories.tsx --max-warnings 0", "upgrade": "npx storybook@latest upgrade && npx storybook automigrate", "check": "npx storybook doctor", "chromatic": "npx chromatic --project-token=chpt_e1fe37492ef1527 --only-changed", "type-check": "npx tsc --noEmit"}, "dependencies": {"@portabletext/react": "^3.1.0", "@stackshift-ui/react": "workspace:*", "@stackshift-ui/system": "workspace:*", "deepmerge": "^4.3.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@chromatic-com/storybook": "^2.0.2", "@storybook/addon-actions": "^8.3.5", "@storybook/addon-docs": "^8.3.5", "@storybook/addon-essentials": "^8.3.5", "@storybook/addon-links": "^8.3.5", "@storybook/manager-api": "^8.3.5", "@storybook/react": "^8.3.5", "@storybook/react-vite": "^8.3.5", "@storybook/theming": "^8.3.5", "@types/node": "^22.0.2", "@vitejs/plugin-react": "^4.2.1", "@stackshift-ui/eslint-config": "workspace:*", "@stackshift-ui/tailwind-config": "workspace:*", "@stackshift-ui/typescript-config": "workspace:*", "autoprefixer": "^10.4.18", "chromatic": "^11.0.8", "eslint": "^8.57.0", "postcss": "^8.4.35", "serve": "^14.2.1", "storybook": "^8.3.5", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.1.4"}}