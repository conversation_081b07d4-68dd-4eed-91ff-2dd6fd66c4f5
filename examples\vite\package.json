{"name": "@example/vite", "version": "0.0.0", "private": false, "scripts": {"build": "vite build", "start": "vite preview", "clean": "rm -rf dist", "dev": "vite --host 0.0.0.0 --port 3001 --clearScreen false", "typecheck": "tsc --noEmit", "lint": "eslint src/"}, "dependencies": {"@stackshift-ui/components": "workspace:*", "nextjs-darkmode": "^1.0.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-mouse-trails": "^0.0.4", "react18-loaders": "latest", "react18-themes": "^3.2.0"}, "devDependencies": {"@stackshift-ui/eslint-config": "workspace:*", "@stackshift-ui/typescript-config": "workspace:*", "@types/react": "^18.3.9", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "typescript": "^5.6.2", "vite": "^5.4.8"}}