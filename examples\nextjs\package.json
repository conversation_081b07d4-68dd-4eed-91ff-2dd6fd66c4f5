{"name": "@example/nextjs", "version": "0.0.0", "private": false, "scripts": {"build": "next build", "clean": "rm -rf .next", "dev": "next dev -p 3002", "lint": "next lint", "typecheck": "tsc --noEmit", "start": "next start"}, "dependencies": {"next": "^14.2.13", "nextjs-darkmode": "^1.0.4", "nextjs-themes": "^4.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-mouse-trails": "^0.0.4", "react18-loaders": "latest", "webgl-generative-particles": "^0.0.1"}, "devDependencies": {"@next/eslint-plugin-next": "^14.2.13", "@stackshift-ui/eslint-config": "workspace:*", "@stackshift-ui/typescript-config": "workspace:*", "@types/node": "^22.7.0", "@types/react": "^18.3.9", "@types/react-dom": "^18.3.0", "typescript": "^5.6.2"}}