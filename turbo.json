{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "storybook-static/**"]}, "test": {"outputs": ["coverage/**"], "dependsOn": ["^build"]}, "lint": {"dependsOn": []}, "typecheck": {"dependsOn": []}, "dev": {"dependsOn": ["^build"], "persistent": true}, "clean": {"cache": false}}}