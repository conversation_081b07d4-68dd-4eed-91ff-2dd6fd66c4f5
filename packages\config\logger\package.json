{"name": "@stackshift-ui/logger", "version": "2.0.8", "private": false, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src/", "typecheck": "tsc --noEmit", "test": "jest"}, "jest": {"preset": "@stackshift-ui/jest-presets/node"}, "devDependencies": {"@stackshift-ui/eslint-config": "workspace:*", "@stackshift-ui/jest-presets": "workspace:*", "@stackshift-ui/typescript-config": "workspace:*", "@types/jest": "^29.5.13", "@types/node": "^22.7.0", "jest": "^29.7.0", "tsup": "^8.3.0", "typescript": "^5.6.2"}}