{"name": "@stackshift-ui/react", "version": "6.0.23", "private": false, "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**", "src"], "scripts": {"build": "tsup && tsc -p tsconfig-build.json", "clean": "rm -rf dist", "dev": "tsup --watch && tsc -p tsconfig-build.json -w", "typecheck": "tsc --noEmit", "lint": "eslint src/", "test": "vitest run --coverage"}, "devDependencies": {"@testing-library/react": "^16.0.1", "@types/node": "^22.7.0", "@types/react": "^18.3.9", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.1.1", "@stackshift-ui/eslint-config": "workspace:*", "@stackshift-ui/typescript-config": "workspace:*", "esbuild-plugin-rdi": "^0.0.0", "esbuild-plugin-react18": "^0.2.5", "esbuild-plugin-react18-css": "^0.0.4", "jsdom": "^25.0.1", "tsup": "^8.3.0", "typescript": "^5.6.2", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.1.1"}, "dependencies": {"@stackshift-ui/app-promo": "workspace:*", "@stackshift-ui/avatar": "workspace:*", "@stackshift-ui/badge": "workspace:*", "@stackshift-ui/blockstyle": "workspace:*", "@stackshift-ui/blog": "workspace:*", "@stackshift-ui/button": "workspace:*", "@stackshift-ui/call-to-action": "workspace:*", "@stackshift-ui/card": "workspace:*", "@stackshift-ui/checkbox": "workspace:*", "@stackshift-ui/checkbox-group": "workspace:*", "@stackshift-ui/contact": "workspace:*", "@stackshift-ui/container": "workspace:*", "@stackshift-ui/cookies": "workspace:*", "@stackshift-ui/faqs": "workspace:*", "@stackshift-ui/features": "workspace:*", "@stackshift-ui/flex": "workspace:*", "@stackshift-ui/footer": "workspace:*", "@stackshift-ui/form": "workspace:*", "@stackshift-ui/form-field": "workspace:*", "@stackshift-ui/grid": "workspace:*", "@stackshift-ui/grid-item": "workspace:*", "@stackshift-ui/header": "workspace:*", "@stackshift-ui/heading": "workspace:*", "@stackshift-ui/how-it-works": "workspace:*", "@stackshift-ui/image": "workspace:*", "@stackshift-ui/input": "workspace:*", "@stackshift-ui/input-file": "workspace:*", "@stackshift-ui/link": "workspace:*", "@stackshift-ui/logo-cloud": "workspace:*", "@stackshift-ui/navigation": "workspace:*", "@stackshift-ui/newsletter": "workspace:*", "@stackshift-ui/portfolio": "workspace:*", "@stackshift-ui/radio": "workspace:*", "@stackshift-ui/radio-group": "workspace:*", "@stackshift-ui/scripts": "workspace:*", "@stackshift-ui/section": "workspace:*", "@stackshift-ui/select": "workspace:*", "@stackshift-ui/signin-signup": "workspace:*", "@stackshift-ui/social-icons": "workspace:*", "@stackshift-ui/statistics": "workspace:*", "@stackshift-ui/stats-card": "workspace:*", "@stackshift-ui/swiper-button": "workspace:*", "@stackshift-ui/swiper-pagination": "workspace:*", "@stackshift-ui/system": "workspace:*", "@stackshift-ui/team": "workspace:*", "@stackshift-ui/testimonial": "workspace:*", "@stackshift-ui/text": "workspace:*", "@stackshift-ui/text-component": "workspace:*", "@stackshift-ui/textarea": "workspace:*", "@stackshift-ui/webriq-form": "workspace:*", "@stackshift-ui/youtube-video": "workspace:*"}, "peerDependencies": {"@types/react": "16.8 - 19", "@stackshift-ui/system": ">=6.0.11", "next": "10 - 14", "react": "16.8 - 19", "react-dom": "16.8 - 19"}, "peerDependenciesMeta": {"next": {"optional": true}}}