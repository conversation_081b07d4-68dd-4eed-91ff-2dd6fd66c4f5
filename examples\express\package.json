{"name": "@example/express", "version": "0.0.0", "private": false, "scripts": {"start": "node dist/index.js", "dev": "tsup --watch --onSuccess \"node dist/index.js\"", "build": "tsup", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "lint": "eslint src/", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jest": {"preset": "@stackshift-ui/jest-presets/node"}, "dependencies": {"@stackshift-ui/logger": "workspace:*", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.0", "morgan": "^1.10.0"}, "devDependencies": {"@stackshift-ui/eslint-config": "workspace:*", "@stackshift-ui/jest-presets": "workspace:*", "@stackshift-ui/typescript-config": "workspace:*", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.13", "@types/morgan": "^1.9.9", "@types/node": "^22.7.0", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "supertest": "^7.0.0", "tsup": "^8.3.0", "typescript": "^5.6.2"}}