name: Publish to NPM

# publish only when package json has changed - assuming version upgrade
on:
  push:
    branches: [main]
    paths: "lib/package.json"

jobs:
  publish:
    # Don't run just after creating repo from template
    # Also avoid running after merging set-up PR
    if: github.run_number > 2 && github.event.repository.owner.login == 'webriq:webriq'
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write

    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: https://registry.npmjs.org
      - name: Setup Git
        run: |
          git config --global user.name webriq
          git config --global user.email "<EMAIL>"
      - run: npm i -g pnpm && pnpm i
        name: Install dependencies
        # fail and not publish if any of the unit tests are failing
      - name: Test
        run: pnpm test
      - name: Copy Readme file
        run: cp ./README.md ./lib # will be uncommented while rebranding
      - name: Apply changesets, publish and create release, branches and tags
        run: node ./scripts/publish.js
        env:
          BRANCH: ${{ github.ref_name }}
          DEFAULT_BRANCH: ${{ github.event.repository.default_branch }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          token: ${{ secrets.GITHUB_TOKEN }}
