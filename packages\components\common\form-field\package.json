{"name": "@stackshift-ui/form-field", "description": "", "version": "6.0.13", "private": false, "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**", "src"], "author": "WebriQ <<EMAIL>>", "scripts": {"build": "tsup && tsc -p tsconfig-build.json", "clean": "rm -rf dist", "dev": "tsup --watch && tsc -p tsconfig-build.json -w", "typecheck": "tsc --noEmit", "lint": "eslint src/", "test": "vitest run --coverage"}, "devDependencies": {"@stackshift-ui/eslint-config": "workspace:*", "@stackshift-ui/typescript-config": "workspace:*", "@testing-library/react": "^16.0.1", "@types/node": "^22.7.0", "@types/react": "^18.3.9", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.1.1", "esbuild-plugin-rdi": "^0.0.0", "esbuild-plugin-react18": "^0.2.5", "esbuild-plugin-react18-css": "^0.0.4", "jsdom": "^25.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "tsup": "^8.3.0", "typescript": "^5.6.2", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.1.1"}, "dependencies": {"@stackshift-ui/scripts": "workspace:*", "@stackshift-ui/system": "workspace:*", "@stackshift-ui/checkbox": "workspace:*", "@stackshift-ui/checkbox-group": "workspace:*", "@stackshift-ui/input": "workspace:*", "@stackshift-ui/input-file": "workspace:*", "@stackshift-ui/radio": "workspace:*", "@stackshift-ui/radio-group": "workspace:*", "@stackshift-ui/select": "workspace:*", "@stackshift-ui/textarea": "workspace:*"}, "peerDependencies": {"@types/react": "16.8 - 19", "next": "10 - 14", "react": "16.8 - 19", "react-dom": "16.8 - 19", "@stackshift-ui/scripts": ">=6.0.10", "@stackshift-ui/system": ">=6.0.11", "@stackshift-ui/checkbox": ">=6.0.11", "@stackshift-ui/checkbox-group": ">=6.0.11", "@stackshift-ui/input": ">=6.0.12", "@stackshift-ui/input-file": ">=6.0.12", "@stackshift-ui/radio": ">=6.0.11", "@stackshift-ui/radio-group": ">=6.0.11", "@stackshift-ui/select": ">=6.0.12", "@stackshift-ui/textarea": ">=6.0.12"}, "peerDependenciesMeta": {"next": {"optional": true}}}