import { cleanup, renderHook, act } from "@testing-library/react";
import { afterEach, describe, test } from "vitest";
import { {{ camelCase name }} } from "./{{kebabCase componentName}}";

describe.concurrent("{{ camelCase name }}", () => {
	afterEach(cleanup);

	test("Dummy test - test if renders without errors", ({ expect }) => {
		const { result } = renderHook(() => {{ camelCase name }}());
    act(() => result.current.setValue(10));
    expect(result.current.value).toBe(10);
	});
});

