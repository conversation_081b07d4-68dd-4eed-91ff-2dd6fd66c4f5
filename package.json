{"name": "turborepo-template", "private": false, "scripts": {"build": "turbo build", "clean": "turbo clean && rm -rf node_modules .turbo", "dev": "turbo run dev", "storybook": "turbo run dev --filter @apps/storybook", "demo": "turbo run dev --filter @apps/nextjs-pages", "doc": "node scripts/doc.js && typedoc", "format": "prettier --write \"**/*.{ts,tsx,md,css,scss}\"", "lint": "turbo lint", "test": "turbo test", "typecheck": "turbo typecheck", "preinstall": "node scripts/update-pm.js", "rebrand": "node scripts/rebrand.js", "next:pages": "turbo run dev --filter @apps/storybook", "changeset": "changeset", "release:status": "changeset status --verbose", "release:version": "npx changeset version", "release": "changeset publish", "generate": "plop"}, "devDependencies": {"@changesets/cli": "^2.28.1", "@stackshift-ui/typescript-config": "workspace:*", "@types/node": "^22.13.10", "enquirer": "^2.4.1", "plop": "^4.0.1", "prettier": "^3.5.3", "turbo": "^2.4.4", "typedoc": "^0.26.11", "typedoc-plugin-extras": "^3.1.0", "typedoc-plugin-inline-sources": "^1.2.1", "typedoc-plugin-mdn-links": "^3.3.8", "typedoc-plugin-missing-exports": "^3.1.0", "typedoc-plugin-rename-defaults": "^0.7.3", "typedoc-plugin-zod": "^1.4.0"}, "packageManager": "pnpm@9.15.4", "engines": {"node": ">=18"}, "pnpm": {"overrides": {"eslint": "^8.0.0"}}, "overrides": {"eslint": "^8.0.0"}}