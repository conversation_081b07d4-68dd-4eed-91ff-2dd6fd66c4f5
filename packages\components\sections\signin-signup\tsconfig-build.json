{
  // using tsc for type declarations as "Note that declaration files generated by any tool other than tsc are not guaranteed to be error-free, so it's a good idea to test the output with tsc" - https://tsup.egoist.dev/#generate-declaration-file
  "extends": "@stackshift-ui/typescript-config/react-library.json",
  "compilerOptions": {
    "outDir": "dist",
    "noEmit": false,
    "emitDeclarationOnly": true,
    "declarationMap": false
  },
  "include": ["src"],
  "exclude": ["dist", "node_modules", "**/*.test.*", "**/*.spec.*"]
}
