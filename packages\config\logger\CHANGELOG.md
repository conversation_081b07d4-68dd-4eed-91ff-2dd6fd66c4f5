# @stackshift-ui/logger

## 2.0.8

### Patch Changes

- f48df11: resolve footer build error
- 0f448cd: revert padding changes
- 9ed91d0: additional qa/qc UI fixes

## 2.0.8-beta.2

### Patch Changes

- revert padding changes

## 2.0.8-beta.1

### Patch Changes

- additional qa/qc UI fixes

## 2.0.8-beta.0

### Patch Changes

- resolve footer build error

## 2.0.7

### Patch Changes

- UI fixes for blog, team, newsletter and features

## 2.0.6

### Patch Changes

- UI fixes for nav, footer, header, cta, signin-signup, app-promo

## 2.0.5

### Patch Changes

- Add fixes for footer D and swiper-pagination styling
- 6b6ece1: Add fixes to footer component and storybook file

## 2.0.5-beta.0

### Patch Changes

- Add fixes to footer component and storybook file

## 2.0.4

### Patch Changes

- Add fixes for footer and team components

## 2.0.3

### Patch Changes

- bump patch version

## 2.0.2

### Patch Changes

- Bump package versions with latest fixes

## 2.0.1

### Patch Changes

- 6737bd1: Bump package versions
- a24f6d2: Add UI fixes for components
- 4932db7: Additional UI fixes from testing

## 2.0.1-beta.2

### Patch Changes

- Additional UI fixes from testing

## 2.0.1-beta.1

### Patch Changes

- Bump package versions

## 2.0.1-beta.0

### Patch Changes

- Add UI fixes for components
