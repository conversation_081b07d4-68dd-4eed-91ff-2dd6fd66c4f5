{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "esnext"], "declaration": true, "sourceMap": true, "moduleResolution": "node", "skipLibCheck": true, "strict": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "jsx": "react-jsx", "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "downlevelIteration": true, "noImplicitAny": false}, "include": ["packages"], "exclude": ["**/node_modules", "**/dist", "**/.turbo"]}