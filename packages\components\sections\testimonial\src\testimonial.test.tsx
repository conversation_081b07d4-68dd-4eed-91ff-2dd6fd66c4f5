import { cleanup, render, screen } from "@testing-library/react";
import { afterEach, describe, test } from "vitest";
import { Testimonial } from "./testimonial";

describe.concurrent("testimonial", () => {
  afterEach(cleanup);

  test.skip("Dummy test - test if renders without errors", ({ expect }) => {
    const clx = "my-class";
    render(<Testimonial />);
    expect(screen.getByTestId("{ kebabCase name }}").classList).toContain(clx);
  });
});
