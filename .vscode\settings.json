{
	// Formatting using <PERSON><PERSON><PERSON> by default for all languages
	"editor.defaultFormatter": "esbenp.prettier-vscode",

	// Ensure enough terminal history is preserved when running tests.
	"terminal.integrated.scrollback": 10000,

	// Disable TypeScript surveys.
	"typescript.surveys.enabled": false,

	"editor.wordWrap": "on",
	"editor.formatOnSave": true,
	"editor.formatOnPaste": true,
	"editor.formatOnSaveMode": "file",

	"editor.codeActionsOnSave": {
    "source.sortImports": "explicit"
  },
}
