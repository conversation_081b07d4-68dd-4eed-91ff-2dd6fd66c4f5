{"$schema": "https://json.schemastore.org/tsconfig", "display": "Remix", "extends": "./base.json", "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2019"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "target": "ES2019", "strict": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": "."}}