{"name": "@example/remix", "version": "0.0.0", "private": false, "sideEffects": false, "scripts": {"build": "remix build", "dev": "remix dev", "typecheck": "tsc --noEmit", "lint": "eslint app/"}, "dependencies": {"@remix-run/node": "^2.12.1", "@remix-run/react": "^2.12.1", "@remix-run/serve": "^2.12.1", "@remix-run/server-runtime": "^2.12.1", "@stackshift-ui/components": "workspace:*", "@vercel/analytics": "^1.3.1", "@vercel/remix-entry-server": "^0.1.1", "nextjs-darkmode": "^1.0.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react18-loaders": "latest", "react18-themes": "^3.2.0"}, "devDependencies": {"@remix-run/dev": "^2.12.1", "@stackshift-ui/eslint-config": "workspace:*", "@stackshift-ui/typescript-config": "workspace:*", "@types/react": "^18.3.9", "@types/react-dom": "^18.3.0", "typescript": "^5.6.2"}, "engines": {"node": ">=18"}}